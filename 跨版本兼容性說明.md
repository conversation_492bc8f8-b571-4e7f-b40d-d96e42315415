# 🔄 跨版本兼容性說明

## ✅ 完全兼容

CPU版本和GPU版本的第一階段結果**完全兼容**，可以混合使用：

### 📋 兼容使用方式

#### 1. CPU第一階段 → GPU第二階段 (推薦)
```bash
# 第一階段用CPU版本（穩定可靠）
python fast_stability_optimizer.py --stage1

# 第二階段用GPU版本（6分鐘完成）
python fast_stability_optimizer-GPUversion.py --stage2
```

#### 2. GPU第一階段 → CPU第二階段
```bash
# 第一階段用GPU版本（更快）
python fast_stability_optimizer-GPUversion.py --stage1

# 第二階段用CPU版本（如GPU有問題）
python fast_stability_optimizer.py --stage2
```

#### 3. 狀態檢查
```bash
# 檢查兩個版本的狀態
python fast_stability_optimizer.py --status
python fast_stability_optimizer-GPUversion.py --status
```

## 🎯 推薦策略

### 最佳實踐
```bash
# 1. 先用CPU版本跑第一階段（14,580組合，約30分鐘）
python fast_stability_optimizer.py --stage1

# 2. 檢查結果滿意度
python fast_stability_optimizer-GPUversion.py --status

# 3. 用GPU版本跑第二階段（3,888組合，約6分鐘）
python fast_stability_optimizer-GPUversion.py --stage2
```

### 為什麼推薦這種方式？

1. **第一階段CPU版本更穩定**
   - 不依賴GPU驅動
   - 錯誤處理更完善
   - 結果更可靠

2. **第二階段GPU版本效果最佳**
   - GPU加速效果最明顯
   - 從30分鐘縮短到6分鐘
   - 5倍速度提升

## 📊 數據格式兼容性

### 共用檔案
- `stage1_coarse_results.csv` - 第一階段結果
- `optimization_checkpoint.json` - 檢查點
- `two_stage_optimization_results.csv` - 最終結果

### 欄位結構
兩版本使用相同的欄位結構：
- `stability_score` - 穩定性分數
- `profit_consistency` - 獲利一致性
- `adx_threshold` - ADX閾值
- `long_fixed_stop_loss_percent` - 多頭停損
- `short_fixed_stop_loss_percent` - 空頭停損
- 等等...

## ⚠️ 注意事項

1. **檔案路徑一致**：兩版本使用相同的檔案路徑
2. **數據結構相同**：CSV格式完全兼容
3. **參數名稱統一**：所有參數名稱一致
4. **檢查點共用**：可以跨版本恢復執行

## 🚀 效能對比

| 階段 | CPU版本 | GPU版本 | 加速比 |
|------|---------|---------|---------|
| 第一階段 | 30分鐘 | 10分鐘 | 3x |
| 第二階段 | 30分鐘 | 6分鐘 | 5x |
| 總計 | 60分鐘 | 16分鐘 | 3.75x |

**推薦組合：CPU第一階段 + GPU第二階段 = 36分鐘**