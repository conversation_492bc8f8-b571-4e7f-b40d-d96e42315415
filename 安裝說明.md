# 📦 最佳化程式安裝說明

## 🚀 快速安裝

### 1. 安裝基本套件
```bash
cd 最佳化
pip install -r requirements.txt
```

### 2. GPU加速套件安裝（可選）

#### 🎮 檢查您的CUDA版本
```bash
nvidia-smi
```
或
```bash
nvcc --version
```

#### 📥 根據CUDA版本安裝CuPy

**CUDA 11.x（推薦GTX1660用戶）**：
```bash
pip install cupy-cuda11x
```

**CUDA 12.x**：
```bash
pip install cupy-cuda12x
```

**不確定CUDA版本**：
```bash
pip install cupy
```

## 🔧 套件說明

### 必需套件
- **pandas** - 數據處理和CSV檔案操作
- **numpy** - 數值計算和陣列操作  
- **tqdm** - 進度條顯示
- **numba** - JIT編譯加速回測計算

### GPU加速套件（可選）
- **cupy-cuda11x/12x** - GPU加速的NumPy替代品
- 如果沒有NVIDIA GPU或安裝失敗，程式會自動使用CPU模式

### 輔助套件
- **psutil** - 系統資源監控
- **argparse** - 命令行參數解析（Python內建）

## ✅ 安裝驗證

### 檢查基本套件
```bash
python -c "import pandas, numpy, tqdm, numba; print('✅ 基本套件安裝成功')"
```

### 檢查GPU加速
```bash
python -c "import cupy; print(f'✅ GPU加速可用: {cupy.cuda.runtime.getDeviceProperties(0)[\"name\"].decode()}')"
```

### 運行測試
```bash
# 進入程式檔案資料夾
cd 程式檔案

# 檢查GPU狀態
python fast_stability_optimizer-GPUversion.py --gpu-info

# 檢查優化狀態  
python fast_stability_optimizer.py --status
```

## 🚨 常見問題

### CuPy安裝失敗
如果遇到CuPy安裝問題：
1. 確認已安裝NVIDIA驅動
2. 確認CUDA版本匹配
3. 可以跳過CuPy，程式會自動使用CPU模式

### CUDA版本不匹配
```bash
# 卸載錯誤版本
pip uninstall cupy-cuda11x cupy-cuda12x cupy

# 重新安裝正確版本
pip install cupy-cuda11x  # 或 cupy-cuda12x
```

### 記憶體不足
如果運行時記憶體不足：
1. 確保關閉其他大型程式
2. GPU版本會自動管理GPU記憶體
3. CPU版本使用8個執行緒，如需要可手動調整

## 🎯 效能建議

- **僅CPU**：使用 `fast_stability_optimizer.py`
- **有GPU**：混合使用（CPU第一階段 + GPU第二階段）
- **GTX1660**：預期3-8倍加速效果

## 🔄 版本要求

- **Python**: 3.8+
- **Windows**: 10/11  
- **GPU**: NVIDIA GTX1660或更新
- **RAM**: 建議8GB+
- **存儲**: 2GB可用空間