# 最佳化程式所需的Python套件
# 安裝指令：pip install -r requirements.txt

# 核心數據處理
pandas>=1.5.0
numpy>=1.21.0

# 進度條顯示
tqdm>=4.64.0

# GPU加速套件（可選）
# 注意：請根據您的CUDA版本選擇正確的cupy版本
# CUDA 11.x 用戶請安裝：cupy-cuda11x
# CUDA 12.x 用戶請安裝：cupy-cuda12x
# 如果不確定CUDA版本，可以跳過cupy安裝，程式會自動回退到CPU模式

# GPU計算加速（適用於GTX1660等NVIDIA顯卡）
cupy-cuda11x>=11.0.0; platform_system != "Darwin"  # Windows/Linux
# cupy-cuda12x>=12.0.0; platform_system != "Darwin"  # 如果使用CUDA 12.x，請註解上一行並取消註解此行

# JIT編譯加速
numba>=0.56.0

# 系統監控（可選）
psutil>=5.9.0

# 其他工具
argparse  # Python 3.2+ 內建，但列出以確保兼容性