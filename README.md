# ETH交易策略最佳化資料夾

## 資料夾結構

```
最佳化/
├── README.md                    # 本說明檔案
├── 程式檔案/                    # 最佳化相關程式
│   ├── staged_optimizer.py      # 分階段參數優化器（主要）
│   ├── fast_stability_optimizer.py  # 快速穩定性優化器
│   ├── dask_distributed_optimizer.py  # Dask分散式優化器
│   ├── time_series_validation.py     # 時間序列驗證
│   ├── test_backtest_logic.py        # 回測邏輯測試
│   └── test_fixed_logic.py           # 修正邏輯測試
├── 結果檔案/                    # 優化結果檔案
│   ├── stage1_coarse_results.csv     # 第一階段粗略搜索結果
│   └── fast_stability_results.csv   # 快速穩定性優化結果
└── 文檔教學/                    # 相關文檔和教學
    └── Dask分散式運算教學.md     # Dask使用教學
```

## 檔案說明

### 程式檔案

**staged_optimizer.py** - 主要的分階段參數優化器
- 第一階段：91,125個參數組合的粗略搜索
- 第二階段：基於第一階段結果進行精細搜索（550萬組合）
- 使用18個CPU核心並行運算
- 修正了資金管理和獲利計算邏輯

**fast_stability_optimizer.py** - 快速穩定性優化器
- 專注於穩定性評分的參數優化
- 適合快速測試和驗證
- 多核心並行處理

**dask_distributed_optimizer.py** - Dask分散式優化器
- 支援多機器叢集運算
- 適合處理大量參數組合（如第二階段的550萬組合）
- 可大幅縮短運算時間

**time_series_validation.py** - 時間序列驗證
- 驗證參數在不同時間段的表現
- 避免過度擬合

**test_backtest_logic.py** - 回測邏輯測試
- 測試回測邏輯的正確性
- 驗證交易信號和資金管理

**test_fixed_logic.py** - 修正邏輯測試
- 測試修正後的交易邏輯
- 確保50%資金投入和1倍槓桿的正確實現

### 結果檔案

**stage1_coarse_results.csv** - 第一階段結果
- 包含91,125個參數組合的測試結果
- 按穩定性評分排序
- 提供第二階段優化的基礎參數

**fast_stability_results.csv** - 快速穩定性優化結果
- 專注於穩定性的優化結果
- 包含詳細的績效指標

### 文檔教學

**Dask分散式運算教學.md** - Dask使用完整教學
- 多機器叢集設置步驟
- 效能估算和故障排除
- 適合處理第二階段的大量運算

## 使用建議

### 1. 第一次使用
```bash
# 執行第一階段優化
cd 程式檔案
python staged_optimizer.py
```

### 2. 分散式運算（推薦用於第二階段）
```bash
# 參考文檔教學設置Dask叢集
cd 文檔教學
# 閱讀 Dask分散式運算教學.md

# 執行分散式優化
cd ../程式檔案
python dask_distributed_optimizer.py
```

### 3. 快速測試
```bash
# 快速穩定性測試
cd 程式檔案
python fast_stability_optimizer.py
```

## 重要注意事項

1. **資金管理邏輯**：所有優化器都使用修正後的邏輯
   - 50%資金投入每筆交易
   - 1倍槓桿（無槓桿）
   - 一次只持有一個方向的倉位

2. **數據依賴**：所有程式都需要
   - `../lookingforthebest/eth_usdt_4h_8years_binance.csv`
   - 8年ETH歷史數據（17,238根K線）

3. **運算資源**：
   - 第一階段：約1-2小時（單機18核心）
   - 第二階段：約76小時（單機）或9-19小時（分散式）

4. **結果解讀**：
   - 穩定性評分越高越好
   - 獲利一致性：正獲利季度比例
   - 平均季度獲利：每季度平均獲利金額
   - 總交易次數：包含開倉+平倉的完整交易

## 更新記錄

- 2025-01-07：創建最佳化資料夾，整理相關檔案
- 2025-01-07：修正資金管理邏輯，實現50%投入+1倍槓桿
- 2025-01-07：完成第一階段優化，準備第二階段分散式運算
