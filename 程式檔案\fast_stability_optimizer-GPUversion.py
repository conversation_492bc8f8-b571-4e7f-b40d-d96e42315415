import pandas as pd
import numpy as np
import itertools
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import time
import json
import os
import sys
import argparse
import warnings

# GPU 依賴庫
try:
    import cupy as cp
    import cupyx.scipy.ndimage as cp_ndimage
    CUPY_AVAILABLE = True
    print("✅ CuPy 已載入，GPU 加速可用")
except ImportError:
    cp = np
    CUPY_AVAILABLE = False
    print("❌ CuPy 未安裝，使用 CPU 計算")
    print("💡 安裝建議：pip install cupy-cuda11x")

try:
    from numba import cuda, jit
    import numba
    NUMBA_CUDA_AVAILABLE = True
    print("✅ Numba CUDA 已載入")
except ImportError:
    NUMBA_CUDA_AVAILABLE = False
    print("❌ Numba CUDA 未安裝，使用 CPU 計算")
    print("💡 安裝建議：pip install numba")

# GPU加速穩定性優化器 - GTX1660專用版本
# 主要特色：
# 1. 使用CuPy加速NumPy技術指標計算
# 2. 使用Numba CUDA加速回測循環
# 3. CPU+GPU協作，最大化GTX1660 (1408核心)性能
# 4. 智能GPU記憶體管理

def check_gpu_status():
    """檢查GPU狀態和可用記憶體"""
    gpu_info = {
        'available': False,
        'device_name': 'CPU Only',
        'memory_total': 0,
        'memory_free': 0,
        'cuda_cores': 0,
        'temperature': None,
        'utilization': None
    }
    
    if CUPY_AVAILABLE:
        try:
            # 檢查CUDA設備
            gpu_info['available'] = True
            gpu_info['device_name'] = cp.cuda.runtime.getDeviceProperties(0)['name'].decode()
            
            # 獲取記憶體資訊
            mempool = cp.get_default_memory_pool()
            gpu_info['memory_total'] = cp.cuda.runtime.memGetInfo()[1] / 1024**3  # GB
            gpu_info['memory_free'] = cp.cuda.runtime.memGetInfo()[0] / 1024**3   # GB
            
            # GTX1660 具體資訊
            if '1660' in gpu_info['device_name']:
                gpu_info['cuda_cores'] = 1408
                print(f"🎮 檢測到 GTX1660！{gpu_info['cuda_cores']} CUDA核心")
            
            print(f"🔧 GPU: {gpu_info['device_name']}")
            print(f"💾 GPU記憶體: {gpu_info['memory_free']:.1f}GB 可用 / {gpu_info['memory_total']:.1f}GB 總計")
            
        except Exception as e:
            print(f"⚠️ GPU檢測失敗: {e}")
            gpu_info['available'] = False
    
    return gpu_info

def optimize_gpu_memory(max_usage_percent=80):
    """優化GPU記憶體使用並監控狀態 - 限制最大使用率"""
    if CUPY_AVAILABLE:
        try:
            # 設定記憶體池
            mempool = cp.get_default_memory_pool()
            pinned_mempool = cp.get_default_pinned_memory_pool()

            # 獲取記憶體使用情況
            memory_total = cp.cuda.runtime.memGetInfo()[1] / 1024**3  # GB
            memory_free_before = cp.cuda.runtime.memGetInfo()[0] / 1024**3   # GB

            # 釋放未使用的記憶體
            mempool.free_all_blocks()
            pinned_mempool.free_all_blocks()

            # 檢查清理後的記憶體
            memory_free_after = cp.cuda.runtime.memGetInfo()[0] / 1024**3   # GB
            memory_freed = memory_free_after - memory_free_before

            print(f"🧹 GPU記憶體已清理，釋放 {memory_freed:.2f}GB")
            print(f"💾 當前可用: {memory_free_after:.1f}GB / {memory_total:.1f}GB")

            # 記憶體使用率檢查和限制
            usage_percent = (memory_total - memory_free_after) / memory_total * 100
            max_allowed_memory = memory_total * (max_usage_percent / 100)
            current_used_memory = memory_total - memory_free_after

            if usage_percent > max_usage_percent:
                print(f"⚠️ GPU記憶體使用率 {usage_percent:.1f}% 超過限制 {max_usage_percent}%")
                print(f"🔧 為系統保留 {100-max_usage_percent}% GPU記憶體")
                # 設定記憶體池限制
                mempool.set_limit(size=int(max_allowed_memory * 1024**3))
                print(f"✅ GPU記憶體使用限制已設定為 {max_allowed_memory:.1f}GB")
            elif usage_percent > 60:
                print(f"💡 GPU記憶體使用率 {usage_percent:.1f}%，為系統保留 {100-max_usage_percent}% 資源")
            else:
                print(f"✅ GPU記憶體使用率正常 {usage_percent:.1f}%")

        except Exception as e:
            print(f"⚠️ GPU記憶體優化失敗: {e}")

def set_gpu_resource_limits(max_memory_percent=80, max_compute_percent=85):
    """設定GPU資源使用限制，為系統保留資源"""
    if CUPY_AVAILABLE:
        try:
            # 設定記憶體限制
            memory_total = cp.cuda.runtime.memGetInfo()[1] / 1024**3  # GB
            max_allowed_memory = memory_total * (max_memory_percent / 100)

            mempool = cp.get_default_memory_pool()
            mempool.set_limit(size=int(max_allowed_memory * 1024**3))

            print(f"🔧 GPU資源限制設定：")
            print(f"   記憶體限制: {max_memory_percent}% ({max_allowed_memory:.1f}GB / {memory_total:.1f}GB)")
            print(f"   為系統保留: {100-max_memory_percent}% 記憶體")
            print(f"   運算限制: {max_compute_percent}% (透過批次大小控制)")

        except Exception as e:
            print(f"⚠️ GPU資源限制設定失敗: {e}")
    else:
        print("💡 GPU不可用，使用CPU模式")

def gpu_ema(prices, span):
    """GPU加速的EMA計算"""
    if CUPY_AVAILABLE and len(prices) > 1000:  # 大數據集使用GPU
        prices_gpu = cp.asarray(prices.values)
        alpha = 2.0 / (span + 1.0)
        
        # 初始化結果陣列
        ema_gpu = cp.zeros_like(prices_gpu)
        ema_gpu[0] = prices_gpu[0]
        
        # GPU並行計算EMA
        for i in range(1, len(prices_gpu)):
            ema_gpu[i] = alpha * prices_gpu[i] + (1 - alpha) * ema_gpu[i-1]
        
        return cp.asnumpy(ema_gpu)
    else:
        # 小數據集或GPU不可用時使用pandas
        return prices.ewm(span=span, adjust=False).mean().values

def gpu_adx_calculation(high, low, close):
    """GPU加速的ADX計算"""
    if CUPY_AVAILABLE and len(high) > 1000:
        # 轉換到GPU
        high_gpu = cp.asarray(high.values)
        low_gpu = cp.asarray(low.values)
        close_gpu = cp.asarray(close.values)
        
        # True Range計算
        high_low = high_gpu - low_gpu
        high_close = cp.abs(high_gpu[1:] - close_gpu[:-1])
        low_close = cp.abs(low_gpu[1:] - close_gpu[:-1])
        
        # 補齊第一個值
        high_close = cp.concatenate([cp.array([0]), high_close])
        low_close = cp.concatenate([cp.array([0]), low_close])
        
        tr_gpu = cp.maximum(high_low, cp.maximum(high_close, low_close))
        
        # Directional Movement計算
        plus_dm_gpu = cp.diff(high_gpu)
        minus_dm_gpu = -cp.diff(low_gpu)
        
        # 補齊第一個值
        plus_dm_gpu = cp.concatenate([cp.array([0]), plus_dm_gpu])
        minus_dm_gpu = cp.concatenate([cp.array([0]), minus_dm_gpu])
        
        plus_dm_gpu = cp.where(plus_dm_gpu < 0, 0, plus_dm_gpu)
        minus_dm_gpu = cp.where(minus_dm_gpu < 0, 0, minus_dm_gpu)
        plus_dm_gpu = cp.where(plus_dm_gpu <= minus_dm_gpu, 0, plus_dm_gpu)
        minus_dm_gpu = cp.where(minus_dm_gpu <= plus_dm_gpu, 0, minus_dm_gpu)
        
        # 移動平均計算
        def gpu_rolling_mean(data, window):
            """GPU加速的移動平均"""
            kernel = cp.ones(window) / window
            return cp.convolve(data, kernel, mode='same')
        
        tr_smooth = gpu_rolling_mean(tr_gpu, 14)
        plus_dm_smooth = gpu_rolling_mean(plus_dm_gpu, 14)
        minus_dm_smooth = gpu_rolling_mean(minus_dm_gpu, 14)
        
        # ADX計算
        plus_di = 100 * (plus_dm_smooth / (tr_smooth + 1e-10))
        minus_di = 100 * (minus_dm_smooth / (tr_smooth + 1e-10))
        dx = 100 * cp.abs(plus_di - minus_di) / (plus_di + minus_di + 1e-10)
        adx = gpu_rolling_mean(dx, 14)
        
        return cp.asnumpy(adx)
    else:
        # CPU回退計算
        high_low = high - low
        high_close = abs(high - close.shift(1))
        low_close = abs(low - close.shift(1))
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        
        plus_dm = high.diff()
        minus_dm = -low.diff()
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0
        plus_dm[(plus_dm <= minus_dm)] = 0
        minus_dm[(minus_dm <= plus_dm)] = 0
        
        tr_smooth = tr.rolling(window=14).mean()
        plus_dm_smooth = plus_dm.rolling(window=14).mean()
        minus_dm_smooth = minus_dm.rolling(window=14).mean()
        
        plus_di = 100 * (plus_dm_smooth / tr_smooth)
        minus_di = 100 * (minus_dm_smooth / tr_smooth)
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        return dx.rolling(window=14).mean().values

def load_and_process_data():
    """載入和處理數據 - GPU加速版本（帶CPU回退）"""
    try:
        print("📊 載入歷史數據...")
        df = pd.read_csv('../../下載歷史K棒/lookingforthebest/eth_usdt_4h_8years_binance.csv', index_col='timestamp', parse_dates=True)
        df.columns = [col.lower() for col in df.columns]
        
        # 限制時間範圍：2020年初到2025年6月底
        start_date = '2020-01-01'
        end_date = '2025-06-30'
        df = df.loc[start_date:end_date]
        
        print(f"時間範圍過濾完成：{start_date} 到 {end_date}")
        print(f"數據範圍：{df.index.min()} 到 {df.index.max()}")
        print(f"數據點數：{len(df):,} 筆")
        
        # 嘗試GPU加速指標計算
        try:
            if CUPY_AVAILABLE:
                print("🚀 嘗試GPU加速技術指標計算...")
                start_time = time.time()
                
                # EMA計算
                print("  📈 計算EMA指標...")
                df['ema90'] = gpu_ema(df['close'], 90)
                df['ema200'] = gpu_ema(df['close'], 200)
                
                # ADX計算
                print("  📊 計算ADX指標...")
                df['adx'] = gpu_adx_calculation(df['high'], df['low'], df['close'])
                
                end_time = time.time()
                print(f"✅ GPU技術指標計算完成，耗時: {end_time - start_time:.2f} 秒")
                
                # 清理GPU記憶體
                optimize_gpu_memory()
                
            else:
                raise RuntimeError("CuPy not available")
                
        except Exception as gpu_error:
            print(f"⚠️ GPU計算失敗，回退到CPU模式: {gpu_error}")
            print("🔄 使用CPU計算技術指標...")
            start_time = time.time()
            
            # CPU回退計算
            df['ema90'] = df['close'].ewm(span=90, adjust=False).mean()
            df['ema200'] = df['close'].ewm(span=200, adjust=False).mean()
            
            # 簡化的ADX計算
            high_low = df['high'] - df['low']
            high_close = abs(df['high'] - df['close'].shift(1))
            low_close = abs(df['low'] - df['close'].shift(1))
            tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            
            plus_dm = df['high'].diff()
            minus_dm = -df['low'].diff()
            plus_dm[plus_dm < 0] = 0
            minus_dm[minus_dm < 0] = 0
            plus_dm[(plus_dm <= minus_dm)] = 0
            minus_dm[(minus_dm <= plus_dm)] = 0
            
            tr_smooth = tr.rolling(window=14).mean()
            plus_dm_smooth = plus_dm.rolling(window=14).mean()
            minus_dm_smooth = minus_dm.rolling(window=14).mean()
            
            plus_di = 100 * (plus_dm_smooth / tr_smooth)
            minus_di = 100 * (minus_dm_smooth / tr_smooth)
            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            df['adx'] = dx.rolling(window=14).mean()
            
            end_time = time.time()
            print(f"✅ CPU技術指標計算完成，耗時: {end_time - start_time:.2f} 秒")
        
        return df.dropna()
        
    except Exception as e:
        print(f"❌ 數據載入失敗: {e}")
        return pd.DataFrame()

def split_by_quarters(df):
    """按季度分割數據"""
    quarters = []
    df['year'] = df.index.year
    df['quarter'] = df.index.quarter
    
    for year in df['year'].unique():
        for quarter in [1, 2, 3, 4]:
            quarter_data = df[(df['year'] == year) & (df['quarter'] == quarter)].copy()
            if len(quarter_data) > 100:
                quarter_data = quarter_data.drop(['year', 'quarter'], axis=1)
                quarters.append({
                    'period': f"{year}Q{quarter}",
                    'data': quarter_data
                })
    return quarters

# GPU加速的回測計算核心函數
if NUMBA_CUDA_AVAILABLE:
    @jit(nopython=True, parallel=True)
    def gpu_backtest_core(close_prices, high_prices, low_prices, ema90_values, ema200_values, adx_values, 
                         adx_threshold, long_fixed_stop, long_trail_activate, long_trail_pullback, long_trail_min,
                         short_fixed_stop, short_trail_activate, short_trail_pullback, short_trail_min):
        """GPU加速的回測核心計算 - 使用Numba JIT編譯"""
        
        # 初始化
        capital = 1000.0
        position_size = 0.0
        entry_price = 0.0
        long_entry_price = 0.0
        long_peak = 0.0
        long_trail_stop_price = 0.0
        is_long_trail_active = False
        short_entry_price = 0.0
        short_trough = 0.0
        short_trail_stop_price = 0.0
        is_short_trail_active = False
        
        trades = 0
        winning_trades = 0
        total_profit = 0.0
        peak_capital = capital
        max_drawdown = 0.0
        
        # 主要回測循環 - JIT編譯加速
        for i in range(len(close_prices)):
            current_close = close_prices[i]
            current_high = high_prices[i]
            current_low = low_prices[i]
            current_adx = adx_values[i]
            current_ema90 = ema90_values[i]
            current_ema200 = ema200_values[i]
            
            # 跳過無效數據
            if np.isnan(current_ema90) or np.isnan(current_ema200) or np.isnan(current_adx):
                continue
            
            strong_trend = current_adx > adx_threshold
            
            # 進場條件
            long_entry_condition = (
                current_close > current_ema90 and
                current_low > current_ema90 and
                current_close > current_ema200 and
                strong_trend
            )
            short_entry_condition = (
                current_close < current_ema90 and
                current_high < current_ema90 and
                current_close < current_ema200 and
                strong_trend
            )
            
            # 多單邏輯
            if long_entry_condition and position_size == 0:
                trade_qty = (capital * 0.7) / current_close
                if trade_qty >= 0.001:
                    position_size = trade_qty
                    entry_price = current_close
                    long_entry_price = current_close
                    long_peak = current_high
                    long_trail_stop_price = 0.0
                    is_long_trail_active = False
                    trades += 1
            
            # 多單管理
            if position_size > 0:
                # 更新峰值
                if current_high > long_peak:
                    long_peak = current_high
                
                # 移動停損激活
                if not is_long_trail_active and current_close > long_entry_price * (1 + long_trail_activate):
                    long_trail_stop_price = long_entry_price * (1 + long_trail_min)
                    is_long_trail_active = True
                
                # 移動停損更新
                if is_long_trail_active and long_peak > 0:
                    new_trail_stop = long_peak * (1 - long_trail_pullback)
                    if long_trail_stop_price == 0 or new_trail_stop > long_trail_stop_price:
                        long_trail_stop_price = new_trail_stop
                
                # 停損觸發
                long_trail_stop_triggered = is_long_trail_active and current_close <= long_trail_stop_price
                long_fixed_stop_triggered = current_close <= long_entry_price * (1 - long_fixed_stop)
                
                if long_trail_stop_triggered or long_fixed_stop_triggered:
                    profit_loss = (current_close - entry_price) * position_size
                    capital += profit_loss
                    total_profit += profit_loss
                    if profit_loss > 0:
                        winning_trades += 1
                    
                    position_size = 0
                    entry_price = 0
                    long_entry_price = 0
                    long_peak = 0
                    long_trail_stop_price = 0
                    is_long_trail_active = False
            
            # 空單邏輯  
            elif short_entry_condition and position_size == 0:
                trade_qty = (capital * 0.7) / current_close
                if trade_qty >= 0.001:
                    position_size = -trade_qty
                    entry_price = current_close
                    short_entry_price = current_close
                    short_trough = current_low
                    short_trail_stop_price = 0.0
                    is_short_trail_active = False
                    trades += 1
            
            # 空單管理
            elif position_size < 0:
                # 更新谷底
                if current_low < short_trough or short_trough == 0:
                    short_trough = current_low
                
                # 移動停損激活
                if not is_short_trail_active and current_close < short_entry_price * (1 - short_trail_activate):
                    short_trail_stop_price = short_entry_price * (1 - short_trail_min)
                    is_short_trail_active = True
                
                # 移動停損更新
                if is_short_trail_active and short_trough > 0:
                    new_trail_stop = short_trough * (1 + short_trail_pullback)
                    if short_trail_stop_price == 0 or new_trail_stop < short_trail_stop_price:
                        short_trail_stop_price = new_trail_stop
                
                # 停損觸發
                short_trail_stop_triggered = is_short_trail_active and current_close >= short_trail_stop_price
                short_fixed_stop_triggered = current_close >= short_entry_price * (1 + short_fixed_stop)
                
                if short_trail_stop_triggered or short_fixed_stop_triggered:
                    profit_loss = (entry_price - current_close) * abs(position_size)
                    capital += profit_loss
                    total_profit += profit_loss
                    if profit_loss > 0:
                        winning_trades += 1
                    
                    position_size = 0
                    entry_price = 0
                    short_entry_price = 0
                    short_trough = 0
                    short_trail_stop_price = 0
                    is_short_trail_active = False
            
            # 更新回撤
            total_value = capital
            if position_size > 0:
                total_value += (current_close - entry_price) * position_size
            elif position_size < 0:
                total_value += (entry_price - current_close) * abs(position_size)
            
            peak_capital = max(peak_capital, total_value)
            if peak_capital > 0:
                current_drawdown = (peak_capital - total_value) / peak_capital
                max_drawdown = max(max_drawdown, current_drawdown)
        
        # 結算最終持倉
        if position_size != 0:
            final_close = close_prices[-1]
            if position_size > 0:
                profit_loss = (final_close - entry_price) * position_size
            else:
                profit_loss = (entry_price - final_close) * abs(position_size)
            capital += profit_loss
            total_profit += profit_loss
            if profit_loss > 0:
                winning_trades += 1
        
        # 計算結果
        win_rate = (winning_trades / trades * 100) if trades > 0 else 0
        profit_factor = 1.0 if trades == 0 else max(1.0, total_profit / max(abs(total_profit - total_profit), 1.0))
        
        return capital, total_profit, trades, win_rate, max_drawdown * 100, profit_factor

else:
    # CPU回退版本
    def gpu_backtest_core(*args):
        """CPU回退版本 - 當Numba不可用時"""
        return None

def run_backtest_single_quarter_gpu(args):
    """GPU加速版本的單季度回測"""
    params, quarter_data, period = args
    
    try:
        # 嘗試使用GPU加速版本
        if NUMBA_CUDA_AVAILABLE and len(quarter_data) > 500:
            # 轉換為NumPy數組以供GPU處理
            close_prices = quarter_data['close'].values
            high_prices = quarter_data['high'].values
            low_prices = quarter_data['low'].values
            ema90_values = quarter_data['ema90'].values
            ema200_values = quarter_data['ema200'].values
            adx_values = quarter_data['adx'].values
            
            # 呼叫GPU加速函數
            result = gpu_backtest_core(
                close_prices, high_prices, low_prices, ema90_values, ema200_values, adx_values,
                params['adx_threshold'],
                params['long_fixed_stop_loss_percent'],
                params['long_trailing_activate_profit_percent'],
                params['long_trailing_pullback_percent'],
                params['long_trailing_min_profit_percent'],
                params['short_fixed_stop_loss_percent'],
                params['short_trailing_activate_profit_percent'],
                params['short_trailing_pullback_percent'],
                params['short_trailing_min_profit_percent']
            )
            
            if result is not None:
                capital, total_profit, trades, win_rate, max_drawdown, profit_factor = result
                return {
                    'period': period,
                    'total_profit': total_profit,
                    'total_trades': trades,
                    'win_rate': win_rate,
                    'max_drawdown_percent': max_drawdown,
                    'profit_factor': profit_factor,
                    'params': params,
                    'gpu_accelerated': True
                }
        
        # 回退到CPU版本
        return run_backtest_single_quarter(args)
        
    except Exception as e:
        print(f"⚠️ GPU回測失敗，回退到CPU: {e}")
        return run_backtest_single_quarter(args)

def run_backtest_single_quarter(args):
    """執行單季度回測（多進程函數）"""
    params, quarter_data, period = args
    
    try:
        # 初始化
        capital = 1000
        position_size = 0
        entry_price = 0
        long_entry_price = None
        long_peak = None
        long_trail_stop_price = None
        is_long_trail_active = False
        short_entry_price = None
        short_trough = None
        short_trail_stop_price = None
        is_short_trail_active = False
        trade_log = []
        peak_capital = capital
        max_drawdown = 0.0
        
        # 回測邏輯
        for _, row in quarter_data.iterrows():
            current_close = row['close']
            current_high = row['high']
            current_low = row['low']
            current_adx = row['adx']
            
            if pd.isna(row['ema90']) or pd.isna(row['ema200']) or pd.isna(current_adx):
                continue
            
            strong_trend = current_adx > params['adx_threshold']
            
            long_entry_condition = (
                current_close > row['ema90'] and
                current_low > row['ema90'] and
                current_close > row['ema200'] and
                strong_trend
            )
            short_entry_condition = (
                current_close < row['ema90'] and
                current_high < row['ema90'] and
                current_close < row['ema200'] and
                strong_trend
            )
            
            # 多單邏輯
            if long_entry_condition and position_size == 0:
                trade_qty = round((capital * 70 / 100) / current_close, 3)
                if trade_qty >= 0.001:
                    position_size = trade_qty
                    entry_price = current_close
                    long_entry_price = current_close
                    long_peak = current_high  # 使用當前K線最高價初始化
                    long_trail_stop_price = None
                    is_long_trail_active = False
                    trade_log.append({'type': 'LONG_ENTRY', 'price': current_close})
            
            if position_size > 0:
                # 確保有進場價格才能執行停損邏輯
                if long_entry_price is None or long_entry_price <= 0:
                    continue
                
                # 確保long_peak不為None
                if long_peak is None:
                    long_peak = current_high
                else:
                    long_peak = max(long_peak, current_high)
                
                # 移動停損激活條件
                if not is_long_trail_active and current_close > long_entry_price * (1 + params['long_trailing_activate_profit_percent']):
                    long_trail_stop_price = long_entry_price * (1 + params['long_trailing_min_profit_percent'])
                    is_long_trail_active = True
                
                # 移動停損更新
                if is_long_trail_active and long_peak is not None:
                    new_trail_stop = long_peak * (1 - params['long_trailing_pullback_percent'])
                    long_trail_stop_price = max(
                        long_trail_stop_price if long_trail_stop_price is not None else 0,
                        new_trail_stop
                    )
                
                # 停損觸發條件 - 使用收盤價判斷，添加 None 檢查
                long_trail_stop_triggered = (
                    is_long_trail_active and 
                    long_trail_stop_price is not None and 
                    current_close <= long_trail_stop_price
                )
                long_fixed_stop_loss_price = long_entry_price * (1 - params['long_fixed_stop_loss_percent'])
                long_fixed_stop_loss_triggered = current_close <= long_fixed_stop_loss_price
                
                if long_trail_stop_triggered or long_fixed_stop_loss_triggered:
                    profit_loss = (current_close - entry_price) * position_size
                    capital += profit_loss
                    trade_log.append({'type': 'LONG_EXIT', 'price': current_close, 'profit_loss': profit_loss})
                    
                    position_size = 0
                    entry_price = 0
                    long_entry_price = None
                    long_peak = None
                    long_trail_stop_price = None
                    is_long_trail_active = False
            
            # 空單邏輯
            if short_entry_condition and position_size == 0:
                trade_qty = round((capital * 70 / 100) / current_close, 3)
                if trade_qty >= 0.001:
                    position_size = -trade_qty
                    entry_price = current_close
                    short_entry_price = current_close
                    short_trough = current_low  # 使用當前K線最低價初始化
                    short_trail_stop_price = None
                    is_short_trail_active = False
                    trade_log.append({'type': 'SHORT_ENTRY', 'price': current_close})
            
            if position_size < 0:
                # 確保有進場價格才能執行停損邏輯
                if short_entry_price is None or short_entry_price <= 0:
                    continue
                
                # 確保short_trough不為None
                if short_trough is None:
                    short_trough = current_low
                else:
                    short_trough = min(short_trough, current_low)
                
                # 移動停損激活條件
                if not is_short_trail_active and current_close < short_entry_price * (1 - params['short_trailing_activate_profit_percent']):
                    short_trail_stop_price = short_entry_price * (1 - params['short_trailing_min_profit_percent'])
                    is_short_trail_active = True
                
                # 移動停損更新
                if is_short_trail_active and short_trough is not None:
                    new_trail_stop = short_trough * (1 + params['short_trailing_pullback_percent'])
                    short_trail_stop_price = min(
                        short_trail_stop_price if short_trail_stop_price is not None else float('inf'),
                        new_trail_stop
                    )
                
                # 停損觸發條件 - 使用收盤價判斷，添加 None 檢查
                short_trail_stop_triggered = (
                    is_short_trail_active and 
                    short_trail_stop_price is not None and 
                    current_close >= short_trail_stop_price
                )
                short_fixed_stop_loss_price = short_entry_price * (1 + params['short_fixed_stop_loss_percent'])
                short_fixed_stop_loss_triggered = current_close >= short_fixed_stop_loss_price
                
                if short_trail_stop_triggered or short_fixed_stop_loss_triggered:
                    profit_loss = (entry_price - current_close) * abs(position_size)
                    capital += profit_loss
                    trade_log.append({'type': 'SHORT_EXIT', 'price': current_close, 'profit_loss': profit_loss})
                    
                    position_size = 0
                    entry_price = 0
                    short_entry_price = None
                    short_trough = None
                    short_trail_stop_price = None
                    is_short_trail_active = False
            
            # 更新回撤
            total_value = capital
            if position_size > 0:
                total_value += (current_close - entry_price) * position_size
            elif position_size < 0:
                total_value += (entry_price - current_close) * abs(position_size)
            
            peak_capital = max(peak_capital, total_value)
            if peak_capital > 0:
                current_drawdown = (peak_capital - total_value) / peak_capital
                max_drawdown = max(max_drawdown, current_drawdown)
        
        # 計算結果
        total_profit = capital - 1000
        exit_trades = [t for t in trade_log if 'EXIT' in t['type']]
        total_trades = len(exit_trades)
        winning_trades = sum(1 for t in exit_trades if t['profit_loss'] > 0)
        losing_trades = total_trades - winning_trades
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        # 計算額外統計資訊
        if total_trades > 0:
            profits = [t['profit_loss'] for t in exit_trades if t['profit_loss'] > 0]
            losses = [t['profit_loss'] for t in exit_trades if t['profit_loss'] < 0]
            
            avg_win = np.mean(profits) if profits else 0
            avg_loss = np.mean(losses) if losses else 0
            profit_factor = abs(sum(profits) / sum(losses)) if losses and sum(losses) != 0 else float('inf')
            avg_trade = total_profit / total_trades
        else:
            avg_win = avg_loss = profit_factor = avg_trade = 0
        
        return {
            'period': period,
            'total_profit': total_profit,
            'max_drawdown_percent': max_drawdown * 100,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_trade': avg_trade,
            'final_capital': capital
        }
    
    except Exception:
        return {
            'period': period,
            'total_profit': -1000,
            'max_drawdown_percent': 100,
            'total_trades': 0,
            'win_rate': 0,
            'final_capital': 0
        }

def test_parameter_combination_gpu(args):
    """GPU加速版本的參數組合測試"""
    params, quarters = args
    
    # 為每個季度準備任務
    quarter_tasks = [(params, quarter['data'], quarter['period']) for quarter in quarters]
    
    # 順序執行各季度，優先使用GPU加速
    quarterly_results = []
    gpu_used = False
    
    for task in quarter_tasks:
        # 嘗試使用GPU加速版本
        if NUMBA_CUDA_AVAILABLE and len(task[1]) > 500:  # 大數據集使用GPU
            result = run_backtest_single_quarter_gpu(task)
            if result and result.get('gpu_accelerated', False):
                gpu_used = True
        else:
            # 小數據集或GPU不可用時使用CPU
            result = run_backtest_single_quarter(task)
        
        if result and result['total_profit'] > -1000:  # 排除失敗的結果
            quarterly_results.append(result)
    
    if not quarterly_results:
        return None
    
    # 計算穩定性指標
    profits = [r['total_profit'] for r in quarterly_results]
    win_rates = [r['win_rate'] for r in quarterly_results]
    trades = [r['total_trades'] for r in quarterly_results]
    drawdowns = [r['max_drawdown_percent'] for r in quarterly_results]
    profit_factors = [r['profit_factor'] for r in quarterly_results]
    
    # 獲利一致性計算
    positive_quarters = sum(1 for p in profits if p > 0)
    total_quarters = len(profits)
    profit_consistency = positive_quarters / total_quarters if total_quarters > 0 else 0
    
    # 穩定性評分算法
    avg_profit = np.mean(profits)
    profit_std = np.std(profits)
    avg_win_rate = np.mean(win_rates)
    max_loss = min(profits)
    avg_drawdown = np.mean(drawdowns)
    avg_profit_factor = np.mean(profit_factors)
    
    # 穩定性分數計算
    consistency_score = profit_consistency * 40
    profit_score = max(0, min(30, avg_profit * 0.1))
    stability_penalty = min(20, profit_std * 0.05) if profit_std > 0 else 0
    drawdown_penalty = min(15, avg_drawdown * 0.3)
    
    stability_score = consistency_score + profit_score - stability_penalty - drawdown_penalty
    stability_score = max(0, stability_score)
    
    # 風險調整回報
    risk_adjusted_return = avg_profit / max(1.0, avg_drawdown) if avg_drawdown > 0 else avg_profit
    
    return {
        'stability_score': stability_score,
        'profit_consistency': profit_consistency,
        'positive_quarters': positive_quarters,
        'total_quarters': total_quarters,
        'avg_profit': avg_profit,
        'max_loss': max_loss,
        'max_drawdown': avg_drawdown,
        'avg_trades': np.mean(trades),
        'total_trades': sum(trades),
        'win_rate': avg_win_rate,
        'avg_win_rate': avg_win_rate,
        'avg_profit_factor': avg_profit_factor,
        'total_profit': sum(profits),
        'risk_adjusted_return': risk_adjusted_return,
        'quarterly_results': quarterly_results,
        'gpu_accelerated': gpu_used,
        **params
    }

def test_parameter_combination(args):
    """測試單個參數組合在所有季度的表現"""
    params, quarters = args
    
    # 為每個季度準備任務
    quarter_tasks = [(params, quarter['data'], quarter['period']) for quarter in quarters]
    
    # 順序執行各季度（避免嵌套多進程）
    quarterly_results = []
    for task in quarter_tasks:
        result = run_backtest_single_quarter(task)
        if result['total_profit'] > -1000:  # 排除失敗的結果
            quarterly_results.append(result)
    
    if not quarterly_results:
        return None
    
    # 計算穩定性指標
    profits = [r['total_profit'] for r in quarterly_results]
    win_rates = [r['win_rate'] for r in quarterly_results]
    trade_counts = [r['total_trades'] for r in quarterly_results]
    drawdowns = [r['max_drawdown_percent'] for r in quarterly_results]
    profit_factors = [r['profit_factor'] for r in quarterly_results if r['profit_factor'] != float('inf')]
    
    avg_profit = np.mean(profits)
    profit_std = np.std(profits)
    positive_quarters = sum(1 for p in profits if p > 0)
    max_loss = min(profits)
    max_drawdown = max(drawdowns) if drawdowns else 0
    profit_consistency = positive_quarters / len(quarterly_results)
    risk_adjusted_return = avg_profit / profit_std if profit_std > 0 else 0
    avg_win_rate = np.mean(win_rates)
    avg_trades = np.mean(trade_counts) if quarterly_results else 0
    avg_profit_factor = np.mean(profit_factors) if profit_factors else 0
    total_profit = sum(profits)
    total_trades = sum(trade_counts)
    
    # 強化穩定性評分（重點：獲利一致性和風險控制）
    stability_score = (
        profit_consistency * 60 +  # 獲利一致性最重要（提高權重）
        max(0, avg_profit) * 0.03 +  # 平均獲利
        max(0, -max_loss) * 0.02 +  # 最大虧損控制
        max(0, 30 - max_drawdown) * 0.5 +  # 回撤控制（回撤越小分數越高）
        risk_adjusted_return * 10 +  # 風險調整回報
        avg_win_rate * 0.2 +  # 平均勝率
        min(avg_profit_factor, 5) * 2  # 獲利因子（上限5倍）
    )
    
    return {
        'params': params,
        'stability_score': stability_score,
        'avg_profit': avg_profit,
        'profit_std': profit_std,
        'positive_quarters': positive_quarters,
        'total_quarters': len(quarterly_results),
        'max_loss': max_loss,
        'max_drawdown': max_drawdown,
        'profit_consistency': profit_consistency,
        'risk_adjusted_return': risk_adjusted_return,
        'avg_win_rate': avg_win_rate,
        'avg_trades': avg_trades,
        'avg_profit_factor': avg_profit_factor,
        'total_profit': total_profit,
        'total_trades': total_trades,
        'quarterly_results': quarterly_results
    }

def run_optimization_stage_batched(parameters, quarters, stage_name="階段", batch_size=30000, max_workers=7):
    """執行分批參數優化 - 避免記憶體過載，限制GPU使用率"""
    # 設定GPU資源限制
    set_gpu_resource_limits(max_memory_percent=80, max_compute_percent=85)

    # 計算組合數量
    keys = list(parameters.keys())
    total_combinations = 1
    for param_values in parameters.values():
        total_combinations *= len(param_values)
    print(f"{stage_name}總共 {total_combinations:,} 個參數組合")

    # 根據GPU記憶體調整批次大小
    if CUPY_AVAILABLE:
        try:
            memory_total = cp.cuda.runtime.memGetInfo()[1] / 1024**3  # GB
            if memory_total < 4:  # 小於4GB記憶體
                batch_size = min(batch_size, 20000)
                max_workers = min(max_workers, 5)
                print(f"🔧 檢測到較小GPU記憶體，調整批次大小為 {batch_size:,}，進程數為 {max_workers}")
            elif memory_total < 6:  # 小於6GB記憶體
                batch_size = min(batch_size, 30000)
                max_workers = min(max_workers, 6)
                print(f"🔧 調整批次大小為 {batch_size:,}，進程數為 {max_workers}")
        except:
            pass

    # 檢查是否需要分批
    if total_combinations <= batch_size:
        print(f"💡 組合數量較少，直接執行")
        return run_optimization_stage_limited(parameters, quarters, stage_name, max_workers)

    # 分批執行
    num_batches = (total_combinations + batch_size - 1) // batch_size
    print(f"🔄 分批執行：{num_batches} 批，每批最多 {batch_size:,} 個組合")
    print(f"🔧 為系統保留 20% GPU記憶體和 15% 運算資源")

    all_results = []
    batch_count = 0

    # 生成器方式生成組合，節省記憶體
    def generate_combinations():
        for combo in itertools.product(*parameters.values()):
            yield dict(zip(keys, combo))

    # 分批處理
    current_batch = []
    for i, params in enumerate(generate_combinations()):
        current_batch.append((params, quarters))

        # 當批次滿了或到達最後一個組合時，執行這一批
        if len(current_batch) >= batch_size or i == total_combinations - 1:
            batch_count += 1
            print(f"\n📦 執行第 {batch_count}/{num_batches} 批 ({len(current_batch):,} 個組合)")

            # 執行當前批次
            batch_results = run_batch_optimization(current_batch, f"{stage_name}-批次{batch_count}", max_workers)
            all_results.extend(batch_results)

            # 清理記憶體並等待一小段時間讓系統喘息
            current_batch = []
            if CUPY_AVAILABLE:
                optimize_gpu_memory(max_usage_percent=80)

            # 批次間短暫休息，讓系統有時間處理其他任務
            import time
            time.sleep(2)

            print(f"✅ 第 {batch_count} 批完成，累計結果: {len(all_results):,}")

    print(f"\n🎉 {stage_name}分批執行完成！總結果: {len(all_results):,}")
    return all_results

def run_batch_optimization(tasks, batch_name, max_workers=6):
    """執行單批次優化 - 限制資源使用"""
    acceleration_mode = "GPU+CPU" if (CUPY_AVAILABLE and NUMBA_CUDA_AVAILABLE) else "CPU"
    print(f"⚡ 開始{batch_name} ({acceleration_mode}加速，{max_workers}進程)...")
    start_time = time.time()

    results = []
    gpu_accelerated_count = 0

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 使用GPU加速版本的回測函數
        if NUMBA_CUDA_AVAILABLE:
            for result in tqdm(executor.map(test_parameter_combination_gpu, tasks),
                              total=len(tasks), desc=f"{batch_name}進度"):
                if result is not None:
                    results.append(result)
                    if result.get('gpu_accelerated', False):
                        gpu_accelerated_count += 1
        else:
            # 回退到CPU版本
            for result in tqdm(executor.map(test_parameter_combination, tasks),
                              total=len(tasks), desc=f"{batch_name}進度"):
                if result is not None:
                    results.append(result)

    end_time = time.time()
    speedup_info = f"，其中 {gpu_accelerated_count} 個使用GPU加速" if gpu_accelerated_count > 0 else ""
    print(f"⏱️ {batch_name}耗時: {end_time - start_time:.2f} 秒{speedup_info}")

    return results

def run_optimization_stage_limited(parameters, quarters, stage_name="階段", max_workers=7):
    """執行單一階段的參數優化（限制資源版本）"""
    # 設定GPU資源限制
    set_gpu_resource_limits(max_memory_percent=80, max_compute_percent=85)

    # 計算組合數量
    keys = list(parameters.keys())
    total_combinations = 1
    for param_values in parameters.values():
        total_combinations *= len(param_values)

    # 生成所有組合
    all_combinations = list(itertools.product(*parameters.values()))

    # 準備任務
    tasks = []
    for combo in all_combinations:
        params = dict(zip(keys, combo))
        tasks.append((params, quarters))

    # CPU+GPU協作執行（限制資源）
    acceleration_mode = "GPU+CPU" if (CUPY_AVAILABLE and NUMBA_CUDA_AVAILABLE) else "CPU"
    print(f"開始{stage_name}多核心優化 ({acceleration_mode}加速，{max_workers}進程)...")
    print(f"🔧 為系統保留 20% GPU記憶體和 15% 運算資源")
    start_time = time.time()

    results = []
    gpu_accelerated_count = 0

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 使用GPU加速版本的回測函數
        if NUMBA_CUDA_AVAILABLE:
            for result in tqdm(executor.map(test_parameter_combination_gpu, tasks),
                              total=len(tasks), desc=f"{stage_name}進度"):
                if result is not None:
                    results.append(result)
                    if result.get('gpu_accelerated', False):
                        gpu_accelerated_count += 1
        else:
            # 回退到CPU版本
            for result in tqdm(executor.map(test_parameter_combination, tasks),
                              total=len(tasks), desc=f"{stage_name}進度"):
                if result is not None:
                    results.append(result)

    end_time = time.time()
    speedup_info = f"，其中 {gpu_accelerated_count} 個使用GPU加速" if gpu_accelerated_count > 0 else ""
    print(f"{stage_name}完成！耗時: {end_time - start_time:.2f} 秒")
    print(f"{stage_name}成功測試 {len(results)} 個參數組合{speedup_info}")

    # 清理GPU記憶體
    if CUPY_AVAILABLE:
        optimize_gpu_memory(max_usage_percent=80)

    return results

def run_optimization_stage(parameters, quarters, stage_name="階段"):
    """執行單一階段的參數優化（原版本，用於小批次）"""
    return run_optimization_stage_limited(parameters, quarters, stage_name, max_workers=7)

def generate_stage2_parameters(best_stage1_result=None, independent_mode=False, batch_mode=False):
    """生成第二階段搜索範圍 - 支援獨立模式和分批模式（優化版本）"""

    if independent_mode:
        print("🚀 第二階段獨立模式：擴大範圍精細搜索")
        print("💡 使用分批執行避免記憶體過載")

        # 平衡搜索範圍和執行時間
        stage2_parameters = {
            'adx_threshold': np.arange(18, 30, 1),  # 12個值（重點範圍）
            'long_fixed_stop_loss_percent': np.arange(0.015, 0.04, 0.003),  # 9個值
            'long_trailing_activate_profit_percent': np.arange(0.015, 0.04, 0.003),  # 9個值
            'long_trailing_pullback_percent': np.arange(0.02, 0.06, 0.005),  # 8個值
            'long_trailing_min_profit_percent': np.arange(0.01, 0.03, 0.003),  # 7個值
            'short_fixed_stop_loss_percent': np.arange(0.015, 0.04, 0.003),  # 9個值
            'short_trailing_activate_profit_percent': np.arange(0.015, 0.04, 0.003),  # 9個值
            'short_trailing_pullback_percent': np.arange(0.02, 0.06, 0.005),  # 8個值
            'short_trailing_min_profit_percent': np.arange(0.01, 0.03, 0.003),  # 7個值
        }

        total_combinations = 1
        print(f"\n🔥 擴大範圍精細搜索參數：")
        for param_name, param_range in stage2_parameters.items():
            total_combinations *= len(param_range)
            print(f"  📊 {param_name}: {len(param_range)} 個值 ({param_range[0]:.3f} 到 {param_range[-1]:.3f}, 步長 {param_range[1]-param_range[0]:.3f})")

        print(f"\n🚀 獨立模式配置（精細版）：")
        print(f"   總參數組合數: {total_combinations:,}")
        print(f"   預估執行時間: {total_combinations * 0.001 / 3600:.1f} 小時 (GPU加速)")
        print(f"   搜索策略: 分批執行，每批 50,000 個組合")
        print(f"   記憶體管理: 自動清理，避免系統崩潰")

        return stage2_parameters
    
    else:
        # 原有模式：基於第一階段結果的細化搜索
        print("🎯 根據第一階段最佳參數生成第二階段細化搜索範圍...")
        
        # 從第一階段最佳結果中提取參數
        best_params = {}
        for key in ['adx_threshold', 'long_fixed_stop_loss_percent', 'long_trailing_activate_profit_percent',
                    'long_trailing_pullback_percent', 'long_trailing_min_profit_percent',
                    'short_fixed_stop_loss_percent', 'short_trailing_activate_profit_percent', 
                    'short_trailing_pullback_percent', 'short_trailing_min_profit_percent']:
            best_params[key] = best_stage1_result[key]
        
        # 第二階段策略：只細化最關鍵的參數，其他參數使用最佳值
        stage2_parameters = {}
        
        # ADX threshold: 最佳值±1（減少範圍）
        best_adx = int(best_params['adx_threshold'])
        stage2_parameters['adx_threshold'] = np.arange(max(15, best_adx-1), min(30, best_adx+2), 1)  # 3個值
        
        # 關鍵參數：停損類參數（影響最大）
        critical_params = [
            'long_fixed_stop_loss_percent',
            'short_fixed_stop_loss_percent', 
            'long_trailing_activate_profit_percent',
            'short_trailing_activate_profit_percent'
        ]
        
        # 次要參數：使用第一階段最佳值
        secondary_params = [
            'long_trailing_pullback_percent',
            'long_trailing_min_profit_percent',
            'short_trailing_pullback_percent', 
            'short_trailing_min_profit_percent'
        ]
        
        # 關鍵參數：細化搜索
        step = 0.002  # 精細步長
        range_width = 0.005  # 較小範圍
        
        param_count_estimate = 1
        
        print(f"\n📊 第二階段參數策略：")
        print(f"  🔥 精細優化參數：{len(critical_params)} 個")
        print(f"  📌 固定參數：{len(secondary_params)} 個")
        
        for param_name, best_value in best_params.items():
            if param_name == 'adx_threshold':
                continue
            
            if param_name in critical_params:
                # 關鍵參數：細化搜索
                min_val = max(0.005, best_value - range_width)
                max_val = min(0.08, best_value + range_width + step)
                param_range = np.arange(min_val, max_val, step)
                param_count_estimate *= len(param_range)
                print(f"  🎯 {param_name}: {len(param_range)} 個值 ({min_val:.3f} 到 {max_val:.3f})")
            else:
                # 次要參數：使用最佳值
                param_range = np.array([best_value])
                print(f"  📌 {param_name}: 固定為最佳值 {best_value:.3f}")
            
            stage2_parameters[param_name] = param_range
        
        total_combinations = len(stage2_parameters['adx_threshold']) * param_count_estimate
        
        print(f"\n🚀 第二階段細化配置：")
        print(f"   總參數組合數: {total_combinations:,}")
        print(f"   預估執行時間: {total_combinations * 0.001 / 60:.1f} 分鐘")
        print(f"   GPU加速模式: {'啟用' if CUPY_AVAILABLE else '未啟用'}")
        
        return stage2_parameters

def save_checkpoint(stage, results, best_params=None):
    """保存檢查點數據"""
    checkpoint_file = '../結果檔案/optimization_checkpoint.json'
    
    checkpoint_data = {
        'stage': stage,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'results_count': len(results),
        'best_params': best_params
    }
    
    with open(checkpoint_file, 'w', encoding='utf-8') as f:
        json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 檢查點已保存：{stage} - {len(results)} 個結果")
    print(f"📁 檢查點檔案：{checkpoint_file}")

def load_checkpoint():
    """載入檢查點數據"""
    checkpoint_file = '../結果檔案/optimization_checkpoint.json'
    
    if not os.path.exists(checkpoint_file):
        return None
    
    try:
        with open(checkpoint_file, 'r', encoding='utf-8') as f:
            checkpoint_data = json.load(f)
        return checkpoint_data
    except Exception as e:
        print(f"❌ 檢查點載入失敗：{e}")
        return None

def load_stage1_results():
    """載入第一階段結果 - 兼容CPU和GPU版本"""
    stage1_file = '../結果檔案/stage1_coarse_results.csv'
    
    if not os.path.exists(stage1_file):
        print(f"❌ 找不到第一階段結果檔案：{stage1_file}")
        print(f"💡 請先執行第一階段：")
        print(f"   CPU版本：python fast_stability_optimizer.py --stage1")
        print(f"   GPU版本：python fast_stability_optimizer-GPUversion.py --stage1")
        return None
    
    try:
        df = pd.read_csv(stage1_file)
        print(f"✅ 成功載入第一階段結果：{len(df)} 個參數組合")
        
        # 檢查必要的欄位
        required_columns = ['stability_score', 'profit_consistency', 'adx_threshold', 
                          'long_fixed_stop_loss_percent', 'short_fixed_stop_loss_percent']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"⚠️ 缺少必要欄位：{missing_columns}")
            print(f"💡 建議重新執行第一階段以確保兼容性")
        else:
            print(f"✅ 數據格式兼容，可以進行第二階段GPU加速優化")
            
        return df
    except Exception as e:
        print(f"❌ 第一階段結果載入失敗：{e}")
        return None

def run_stage1_only():
    """只執行第一階段優化"""
    print("=== 第一階段優化模式 ===")
    print(f"檢測到 {multiprocessing.cpu_count()} 個CPU核心，將使用 8 個進程")
    
    # 載入數據
    print("載入和處理數據...")
    df_processed = load_and_process_data()
    if df_processed.empty:
        return False
    
    print(f"數據處理完成，共 {len(df_processed)} 筆記錄")
    
    # 按季度分割
    quarters = split_by_quarters(df_processed)
    print(f"數據分為 {len(quarters)} 個季度")
    
    # 第一階段參數 - 中等範圍：1000萬~3000萬組合
    stage1_parameters = {
        'adx_threshold': np.arange(16, 32, 2),  # 8個值（16,18,20,22,24,26,28,30）
        'long_fixed_stop_loss_percent': np.arange(0.012, 0.045, 0.006),  # 6個值
        'long_trailing_activate_profit_percent': np.arange(0.012, 0.045, 0.006),  # 6個值
        'long_trailing_pullback_percent': np.arange(0.018, 0.065, 0.012),  # 4個值
        'long_trailing_min_profit_percent': np.arange(0.008, 0.032, 0.008),  # 4個值
        'short_fixed_stop_loss_percent': np.arange(0.012, 0.045, 0.006),  # 6個值
        'short_trailing_activate_profit_percent': np.arange(0.012, 0.045, 0.006),  # 6個值
        'short_trailing_pullback_percent': np.arange(0.018, 0.065, 0.012),  # 4個值
        'short_trailing_min_profit_percent': np.arange(0.008, 0.032, 0.008),  # 4個值
    }
    # 總組合數: 8×6×6×4×4×6×6×4×4 = 16,588,800 個組合（約1660萬個，在您的接受範圍內）
    
    # 執行第一階段 - 使用分批優化
    print("\n=== 第一階段：改進搜索 ===")
    stage1_results = run_optimization_stage_batched(stage1_parameters, quarters, stage_name="第一階段", batch_size=35000, max_workers=7)
    
    if not stage1_results:
        print("❌ 第一階段沒有找到有效結果")
        return False
    
    # 保存第一階段結果（參數已展開到根層級）
    stage1_df = pd.DataFrame(stage1_results)
    stage1_df = stage1_df.sort_values('stability_score', ascending=False)
    stage1_df.to_csv('../結果檔案/stage1_coarse_results.csv', index=False)
    
    # 找出最佳結果
    best_stage1 = stage1_df.iloc[0]
    best_params = {}
    for key in ['adx_threshold', 'long_fixed_stop_loss_percent', 'long_trailing_activate_profit_percent',
                'long_trailing_pullback_percent', 'long_trailing_min_profit_percent',
                'short_fixed_stop_loss_percent', 'short_trailing_activate_profit_percent', 
                'short_trailing_pullback_percent', 'short_trailing_min_profit_percent']:
        best_params[key] = float(best_stage1[key])
    
    # 保存檢查點
    save_checkpoint("stage1_completed", stage1_results, best_params)
    
    print(f"\n🎉 第一階段完成！")
    print(f"📊 測試了 {len(stage1_results)} 個參數組合")
    print(f"🏆 最佳穩定性分數: {best_stage1['stability_score']:.2f}")
    print(f"💰 最佳獲利一致性: {best_stage1['profit_consistency']:.1%}")
    print(f"📁 結果已保存到 stage1_coarse_results.csv")
    print(f"\n要執行第二階段，請運行：python fast_stability_optimizer.py --stage2")
    
    return True

def run_stage2_only():
    """只執行第二階段優化"""
    print("=== 第二階段優化模式 ===")
    
    # 檢查檢查點
    checkpoint = load_checkpoint()
    if not checkpoint or checkpoint['stage'] != 'stage1_completed':
        print("❌ 找不到第一階段完成的檢查點，請先執行第一階段")
        return False
    
    # 載入第一階段結果
    stage1_df = load_stage1_results()
    if stage1_df is None:
        return False
    
    best_stage1 = stage1_df.iloc[0]
    print(f"✅ 載入第一階段最佳結果：穩定性分數 {best_stage1['stability_score']:.2f}")
    
    # 載入數據
    print("載入和處理數據...")
    df_processed = load_and_process_data()
    if df_processed.empty:
        return False
    
    # 按季度分割
    quarters = split_by_quarters(df_processed)
    print(f"數據分為 {len(quarters)} 個季度")
    
    # 生成第二階段參數
    print("\n=== 第二階段：細化搜索 ===")
    stage2_parameters = generate_stage2_parameters(best_stage1)
    
    # 執行第二階段 - 使用分批優化和資源限制
    stage2_results = run_optimization_stage_batched(stage2_parameters, quarters, stage_name="第二階段", batch_size=25000, max_workers=6)
    
    if not stage2_results:
        print("❌ 第二階段沒有找到有效結果")
        return False
    
    # 保存第二階段結果（參數已展開到根層級）
    stage2_df = pd.DataFrame(stage2_results)
    stage2_df = stage2_df.sort_values('stability_score', ascending=False)
    stage2_df.to_csv('../結果檔案/stage2_fine_results.csv', index=False)
    
    # 合併結果
    stage1_results = stage1_df.to_dict('records')
    final_results = stage1_results + stage2_results
    final_df = pd.DataFrame(final_results)
    final_df = final_df.sort_values('stability_score', ascending=False)
    final_df.to_csv('../結果檔案/two_stage_optimization_results.csv', index=False)
    
    # 保存完成檢查點
    save_checkpoint("stage2_completed", final_results)
    
    # 顯示最終結果
    best_result = final_df.iloc[0]
    print(f"\n🎉 第二階段完成！")
    print(f"📊 第二階段測試了 {len(stage2_results)} 個參數組合")
    print(f"🏆 最終最佳穩定性分數: {best_result['stability_score']:.2f}")
    print(f"💰 最終最佳獲利一致性: {best_result['profit_consistency']:.1%}")
    print(f"📁 最終結果已保存到 two_stage_optimization_results.csv")
    
    return True

def run_independent_stage2():
    """獨立第二階段：大範圍全域搜索"""
    print("=== 獨立第二階段：大範圍搜索模式 ===")
    print("🚀 GPU超快速度，執行大範圍全域參數優化")
    
    # 載入數據
    print("載入和處理數據...")
    df_processed = load_and_process_data()
    if df_processed.empty:
        return False
    
    print(f"數據處理完成，共 {len(df_processed)} 筆記錄")
    
    # 按季度分割
    quarters = split_by_quarters(df_processed)
    print(f"數據分為 {len(quarters)} 個季度")
    
    # 生成獨立第二階段參數（大範圍）
    print("\n=== 獨立第二階段：大範圍全域搜索 ===")
    stage2_parameters = generate_stage2_parameters(independent_mode=True)
    
    # 執行大範圍優化 - 使用分批優化和資源限制
    stage2_results = run_optimization_stage_batched(stage2_parameters, quarters, stage_name="獨立第二階段", batch_size=20000, max_workers=6)
    
    if not stage2_results:
        print("❌ 獨立第二階段沒有找到有效結果")
        return False
    
    # 保存獨立第二階段結果
    stage2_df = pd.DataFrame(stage2_results)
    stage2_df = stage2_df.sort_values('stability_score', ascending=False)
    stage2_df.to_csv('../結果檔案/independent_stage2_results.csv', index=False)
    
    # 保存為最終結果
    stage2_df.to_csv('../結果檔案/independent_optimization_results.csv', index=False)
    
    # 保存檢查點
    save_checkpoint("independent_stage2_completed", stage2_results)
    
    # 顯示最佳結果
    best_result = stage2_df.iloc[0]
    print(f"\n🎉 獨立第二階段完成！")
    print(f"📊 測試了 {len(stage2_results)} 個參數組合")
    print(f"🏆 最佳穩定性分數: {best_result['stability_score']:.2f}")
    print(f"💰 最佳獲利一致性: {best_result['profit_consistency']:.1%}")
    print(f"🎯 最佳ADX閾值: {best_result['adx_threshold']}")
    print(f"💡 最佳多頭停損: {best_result['long_fixed_stop_loss_percent']:.3f}")
    print(f"💡 最佳空頭停損: {best_result['short_fixed_stop_loss_percent']:.3f}")
    
    print(f"\n📁 結果已保存到：")
    print(f"   - independent_stage2_results.csv (詳細結果)")
    print(f"   - independent_optimization_results.csv (最終結果)")
    
    return True

def main():
    """主函數：GPU加速的兩階段參數優化"""
    print("🚀 GPU加速參數優化器啟動中...")

    # 初始化GPU並檢查狀態
    gpu_info = check_gpu_status()

    # 顯示加速狀態
    if gpu_info['available']:
        print(f"⚡ GPU加速已啟用：{gpu_info['device_name']}")
        if gpu_info['cuda_cores'] > 0:
            print(f"🔥 CUDA核心數：{gpu_info['cuda_cores']}")
        print(f"💾 可用GPU記憶體：{gpu_info['memory_free']:.1f}GB")

        # 設定GPU資源限制，為系統保留資源
        print("🔧 設定GPU資源限制，為系統保留 20% 記憶體...")
        set_gpu_resource_limits(max_memory_percent=80, max_compute_percent=85)
    else:
        print("⚠️ 使用CPU模式，建議安裝CuPy和Numba以啟用GPU加速")
        print("💡 安裝指令：pip install cupy-cuda11x numba")
    
    parser = argparse.ArgumentParser(description='GPU加速參數優化器')
    parser.add_argument('--stage1', action='store_true', help='只執行第一階段優化')
    parser.add_argument('--stage2', action='store_true', help='只執行第二階段優化（基於第一階段）')
    parser.add_argument('--independent', action='store_true', help='獨立第二階段：大範圍全域搜索')
    parser.add_argument('--full', action='store_true', help='執行完整兩階段優化（預設）')
    parser.add_argument('--status', action='store_true', help='檢查當前優化狀態')
    parser.add_argument('--gpu-info', action='store_true', help='顯示GPU詳細資訊')
    
    args = parser.parse_args()
    
    # GPU資訊查詢
    if args.gpu_info:
        if CUPY_AVAILABLE:
            try:
                print(f"\n🎮 GPU詳細資訊：")
                print(f"設備名稱：{gpu_info['device_name']}")
                print(f"總記憶體：{gpu_info['memory_total']:.1f}GB")
                print(f"可用記憶體：{gpu_info['memory_free']:.1f}GB")
                print(f"CuPy版本：{cp.__version__}")
                if NUMBA_CUDA_AVAILABLE:
                    print(f"Numba版本：{numba.__version__}")
                print(f"CUDA加速：{'✅ 可用' if NUMBA_CUDA_AVAILABLE else '❌ 不可用'}")
            except:
                print("❌ 無法獲取GPU詳細資訊")
        else:
            print("❌ GPU不可用")
        return
    
    # 檢查狀態
    if args.status:
        checkpoint = load_checkpoint()
        stage1_file = '../結果檔案/stage1_coarse_results.csv'
        stage1_exists = os.path.exists(stage1_file)
        
        print(f"\n📊 當前優化狀態：")
        
        if checkpoint:
            print(f"✅ 找到檢查點：{checkpoint['stage']} ({checkpoint['timestamp']})")
            print(f"📊 結果數量：{checkpoint['results_count']}")
        else:
            print(f"❌ 沒有找到檢查點")
            
        if stage1_exists:
            try:
                df = pd.read_csv(stage1_file)
                print(f"✅ 第一階段結果存在：{len(df)} 個參數組合")
                print(f"💡 可以執行第二階段GPU加速：")
                print(f"   python fast_stability_optimizer-GPUversion.py --stage2")
            except:
                print(f"❌ 第一階段結果檔案損壞")
        else:
            print(f"❌ 第一階段結果不存在")
            print(f"💡 可以執行第一階段：")
            print(f"   CPU版本：python fast_stability_optimizer.py --stage1")
            print(f"   GPU版本：python fast_stability_optimizer-GPUversion.py --stage1")
            
        print(f"\n🔄 跨版本兼容性：")
        print(f"✅ CPU版本第一階段結果 → GPU版本第二階段 (推薦)")
        print(f"✅ GPU版本第一階段結果 → CPU版本第二階段")
        print(f"✅ 混合使用兩個版本完全沒問題")
        return
    
    # 選擇執行模式
    if args.stage1:
        success = run_stage1_only()
        if success:
            print("\n✅ 第一階段完成，可以用以下命令繼續第二階段：")
            print("python fast_stability_optimizer.py --stage2")
    elif args.stage2:
        success = run_stage2_only()
        if success:
            print("\n🎉 兩階段優化全部完成！")
    elif args.independent:
        success = run_independent_stage2()
        if success:
            print("\n🎉 獨立第二階段大範圍搜索完成！")
    else:
        # 預設執行完整兩階段優化
        print("=== 完整兩階段優化模式 ===")
        print("將連續執行第一階段和第二階段")
        print("提示：如需中斷後恢復，可使用 --stage1 和 --stage2 參數")
        
        # 執行第一階段
        success_stage1 = run_stage1_only()
        if not success_stage1:
            print("❌ 第一階段失敗，程式結束")
            return
        
        print(f"\n⏳ 等待5秒後開始第二階段...")
        time.sleep(5)
        
        # 執行第二階段
        success_stage2 = run_stage2_only()
        if success_stage2:
            print("\n🎉 完整兩階段優化完成！")
        else:
            print("❌ 第二階段失敗")

if __name__ == "__main__":
    main()
